import { LiquidWeb } from 'liquid-web/react';

export default () => {
  const options = {
    init: true,
    scale: 22,
    blur: 2,
    saturation: 170,
    aberration: 50,
    mode: 'standard' as const,
  };

  return (
    <div
      style={{
        minHeight: '100vh',
        backgroundImage: 'url(https://images.unsplash.com/photo-1506184155123-73f3a6dfc2fc?q=80&w=764&auto=format&fit=crop&ixlib=rb-4.1.0&ixid=M3wxMjA3fDB8MHxwaG90by1wYWdlfHx8fGVufDB8fHx8fA%3D%3D)',
        backgroundSize: 'cover',
        backgroundPosition: 'center',
        backgroundRepeat: 'no-repeat',
        display: 'flex',
        alignItems: 'center',
        justifyContent: 'center',
        padding: '2rem'
      }}
    >
      <LiquidWeb
        options={options}
        selector="div"
        onClick={(instance) => console.log('Clicked!', instance)}
        onMouseEnter={(instance) => console.log('Mouse entered!', instance)}
        onMouseLeave={(instance) => console.log('Mouse left!', instance)}
        style={{
          padding: '2rem',
          borderRadius: '12px',
          backgroundColor: 'rgba(255, 255, 255, 0.1)',
          backdropFilter: 'blur(10px)',
          border: '1px solid rgba(255, 255, 255, 0.2)'
        }}
      >
        <button
          style={{
            padding: '12px 24px',
            fontSize: '16px',
            fontWeight: '600',
            color: 'white',
            backgroundColor: 'rgba(255, 255, 255, 0.2)',
            border: '1px solid rgba(255, 255, 255, 0.3)',
            borderRadius: '8px',
            cursor: 'pointer',
            backdropFilter: 'blur(5px)',
            transition: 'all 0.3s ease'
          }}
        >
          I am liquid button!
        </button>
      </LiquidWeb>
    </div>
  );
};