
import { useState } from 'react'
import { LiquidWeb } from 'liquid-web/react'

function App() {
  const [options, setOptions] = useState({
    scale: 38,
    blur: 50,
    saturation: 141,
    aberration: 200,
    mode: 'standard' as const
  })

  const handleOptionChange = (key: string, value: number | string) => {
    setOptions(prev => ({ ...prev, [key]: value }))
  }

  return (
    <div className="min-h-screen relative flex items-center justify-center p-8">
      {/* Background Image */}
      <div
        className="absolute inset-0 bg-cover bg-center bg-no-repeat"
        style={{
          backgroundImage: 'url(https://images.unsplash.com/photo-1503435980610-a51f3ddfee50?q=80&w=687&auto=format&fit=crop&ixlib=rb-4.1.0&ixid=M3wxMjA3fDB8MHxwaG90by1wYWdlfHx8fGVufDB8fHx8fA%3D%3D)'
        }}
      />

      {/* Glass Panel with Liquid Web Effect */}
      <div className="relative z-10">
        <LiquidWeb
          options={options}
          selector="div"
          onMouseEnter={(instance) => console.log('Mouse entered!', instance)}
          onMouseLeave={(instance) => console.log('Mouse left!', instance)}
          onClick={(instance) => console.log('Clicked!', instance)}
        >
          <GlassPanel options={options} onOptionChange={handleOptionChange} />
        </LiquidWeb>
      </div>
    </div>
  )
}

// Glass Panel Component with Interactive Controls
function GlassPanel({
  options,
  onOptionChange
}: {
  options: any,
  onOptionChange: (key: keyof typeof options, value: number | string) => void
}) {
  const modes = ['standard', 'polar', 'prominent', 'shader']

  return (
    <div className="w-96 p-6 rounded-2xl backdrop-blur-md bg-white/10 border border-white/20 shadow-xl">
      {/* Mode Section */}
      <div className="mb-6">
        <label className="block text-white text-sm font-medium mb-2">Mode</label>
        <div className="grid grid-cols-2 gap-2">
          {modes.map((mode) => (
            <button
              key={mode}
              onClick={() => onOptionChange('mode', mode)}
              className={`px-4 py-2 rounded-lg text-sm font-medium transition-all ${
                options.mode === mode
                  ? 'bg-green-600/80 text-white'
                  : 'bg-white/10 text-white/70 hover:bg-white/20'
              }`}
            >
              {mode.charAt(0).toUpperCase() + mode.slice(1)}
            </button>
          ))}
        </div>
      </div>

      {/* Scale Section */}
      <div className="mb-6">
        <div className="flex justify-between items-center mb-2">
          <label className="text-white text-sm font-medium">Scale</label>
          <span className="text-white text-sm">{options.scale}</span>
        </div>
        <div className="relative">
          <input
            type="range"
            min="0"
            max="200"
            value={options.scale}
            onChange={(e) => onOptionChange('scale', parseInt(e.target.value))}
            className="w-full h-2 bg-white/20 rounded-lg appearance-none cursor-pointer slider"
          />
          <div className="flex justify-between text-white/60 text-xs mt-1">
            <span>0</span>
            <span>200</span>
          </div>
        </div>
      </div>

      {/* Blur Section */}
      <div className="mb-6">
        <div className="flex justify-between items-center mb-2">
          <label className="text-white text-sm font-medium">Blur</label>
          <span className="text-white text-sm">{options.blur}</span>
        </div>
        <div className="relative">
          <input
            type="range"
            min="0"
            max="50"
            value={options.blur}
            onChange={(e) => onOptionChange('blur', parseInt(e.target.value))}
            className="w-full h-2 bg-white/20 rounded-lg appearance-none cursor-pointer slider"
          />
          <div className="flex justify-between text-white/60 text-xs mt-1">
            <span>0</span>
            <span>50</span>
          </div>
        </div>
      </div>

      {/* Saturation Section */}
      <div className="mb-6">
        <div className="flex justify-between items-center mb-2">
          <label className="text-white text-sm font-medium">Saturation</label>
          <span className="text-white text-sm">{options.saturation}</span>
        </div>
        <div className="relative">
          <input
            type="range"
            min="0"
            max="300"
            value={options.saturation}
            onChange={(e) => onOptionChange('saturation', parseInt(e.target.value))}
            className="w-full h-2 bg-white/20 rounded-lg appearance-none cursor-pointer slider"
          />
          <div className="flex justify-between text-white/60 text-xs mt-1">
            <span>0</span>
            <span>300</span>
          </div>
        </div>
      </div>

      {/* Aberration Section */}
      <div>
        <div className="flex justify-between items-center mb-2">
          <label className="text-white text-sm font-medium">Aberration</label>
          <span className="text-white text-sm">{options.aberration}</span>
        </div>
        <div className="relative">
          <input
            type="range"
            min="0"
            max="1000"
            value={options.aberration}
            onChange={(e) => onOptionChange('aberration', parseInt(e.target.value))}
            className="w-full h-2 bg-white/20 rounded-lg appearance-none cursor-pointer slider"
          />
          <div className="flex justify-between text-white/60 text-xs mt-1">
            <span>0</span>
            <span>1000</span>
          </div>
        </div>
      </div>
    </div>
  )
}

export default App