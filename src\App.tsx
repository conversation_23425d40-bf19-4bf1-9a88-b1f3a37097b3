import { LiquidWeb } from 'liquid-web/react';

export default () => {
  const options = {
    init: true,
    scale: 22,
    blur: 2,
    saturation: 170,
    aberration: 50,
    mode: 'standard' as const,
  };

  const backgroundImageUrl = 'https://images.unsplash.com/photo-1506184155123-73f3a6dfc2fc?q=80&w=764&auto=format&fit=crop&ixlib=rb-4.1.0&ixid=M3wxMjA3fDB8MHxwaG90by1wYWdlfHx8fGVufDB8fHx8fA%3D%3D';

  return (
    <div>
      {/* First section with background */}
      <div
        style={{
          minHeight: '100vh',
          backgroundImage: `url(${backgroundImageUrl})`,
          backgroundSize: 'cover',
          backgroundPosition: 'center',
          backgroundRepeat: 'no-repeat',
          display: 'flex',
          alignItems: 'center',
          justifyContent: 'center',
          padding: '2rem'
        }}
      >
        <LiquidWeb
          options={options}
          selector="div"
          onClick={(instance) => console.log('Clicked!', instance)}
          onMouseEnter={(instance) => console.log('Mouse entered!', instance)}
          onMouseLeave={(instance) => console.log('Mouse left!', instance)}
          style={{
            padding: '2rem',
            borderRadius: '12px',
            backgroundColor: 'rgba(255, 255, 255, 0.1)',
            backdropFilter: 'blur(10px)',
            border: '1px solid rgba(255, 255, 255, 0.2)'
          }}
        >
          <button
            style={{
              padding: '12px 24px',
              fontSize: '16px',
              fontWeight: '600',
              color: 'white',
              backgroundColor: 'rgba(255, 255, 255, 0.2)',
              border: '1px solid rgba(255, 255, 255, 0.3)',
              borderRadius: '8px',
              cursor: 'pointer',
              backdropFilter: 'blur(5px)',
              transition: 'all 0.3s ease'
            }}
          >
            I am liquid button!
          </button>
        </LiquidWeb>
      </div>

      {/* Content section */}
      <div
        style={{
          minHeight: '100vh',
          backgroundColor: 'rgba(0, 0, 0, 0.8)',
          color: 'white',
          padding: '4rem 2rem',
          display: 'flex',
          flexDirection: 'column',
          alignItems: 'center',
          justifyContent: 'center',
          textAlign: 'center'
        }}
      >
        <h1 style={{ fontSize: '3rem', marginBottom: '2rem', fontWeight: '300' }}>
          Liquid Glass Experience
        </h1>
        <p style={{ fontSize: '1.2rem', maxWidth: '600px', lineHeight: '1.6', marginBottom: '3rem' }}>
          Scroll down to see more liquid glass effects with the beautiful landscape background.
          This creates an immersive scrolling experience with stunning visual effects.
        </p>

        <LiquidWeb
          options={{...options, scale: 15}}
          selector="div"
          onClick={(instance) => console.log('Second liquid clicked!', instance)}
          style={{
            padding: '1.5rem 3rem',
            borderRadius: '16px',
            backgroundColor: 'rgba(255, 255, 255, 0.05)',
            backdropFilter: 'blur(15px)',
            border: '1px solid rgba(255, 255, 255, 0.1)',
            marginBottom: '2rem'
          }}
        >
          <h3 style={{ margin: 0, fontSize: '1.5rem', fontWeight: '400' }}>
            Another Liquid Element
          </h3>
        </LiquidWeb>
      </div>

      {/* Second background section */}
      <div
        style={{
          minHeight: '100vh',
          backgroundImage: `url(${backgroundImageUrl})`,
          backgroundSize: 'cover',
          backgroundPosition: 'center bottom',
          backgroundRepeat: 'no-repeat',
          display: 'flex',
          alignItems: 'center',
          justifyContent: 'center',
          padding: '2rem',
          flexDirection: 'column',
          gap: '2rem'
        }}
      >
        <LiquidWeb
          options={{...options, mode: 'polar' as const}}
          selector="div"
          onClick={(instance) => console.log('Polar mode clicked!', instance)}
          style={{
            padding: '2rem',
            borderRadius: '20px',
            backgroundColor: 'rgba(255, 255, 255, 0.15)',
            backdropFilter: 'blur(12px)',
            border: '1px solid rgba(255, 255, 255, 0.25)',
            textAlign: 'center'
          }}
        >
          <h2 style={{ color: 'white', margin: '0 0 1rem 0', fontSize: '2rem', fontWeight: '300' }}>
            Polar Mode Effect
          </h2>
          <p style={{ color: 'rgba(255, 255, 255, 0.9)', margin: 0, fontSize: '1.1rem' }}>
            Experience different liquid glass modes
          </p>
        </LiquidWeb>

        <LiquidWeb
          options={{...options, mode: 'prominent' as const, scale: 30}}
          selector="div"
          onClick={(instance) => console.log('Prominent mode clicked!', instance)}
          style={{
            padding: '1.5rem 2.5rem',
            borderRadius: '12px',
            backgroundColor: 'rgba(0, 0, 0, 0.3)',
            backdropFilter: 'blur(8px)',
            border: '1px solid rgba(255, 255, 255, 0.2)'
          }}
        >
          <button
            style={{
              padding: '10px 20px',
              fontSize: '14px',
              fontWeight: '500',
              color: 'white',
              backgroundColor: 'transparent',
              border: '1px solid rgba(255, 255, 255, 0.4)',
              borderRadius: '6px',
              cursor: 'pointer',
              backdropFilter: 'blur(3px)'
            }}
          >
            Prominent Mode Button
          </button>
        </LiquidWeb>
      </div>
    </div>
  );
};